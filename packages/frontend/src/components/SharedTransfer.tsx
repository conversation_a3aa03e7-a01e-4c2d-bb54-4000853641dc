import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Download, FileText, Lock, AlertCircle, CheckCircle, Clock, Zap, Heart, Eye, EyeOff, Archive, File } from 'lucide-react';
import { getTransferInfo, downloadCompressedFile, downloadOriginalFile } from '../services/api';
import type { TransferInfo } from '../services/api';

export default function SharedTransfer() {
  const { transferId } = useParams<{ transferId: string }>();
  const [transferInfo, setTransferInfo] = useState<TransferInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showPasswordInput, setShowPasswordInput] = useState(false);
  const [downloading, setDownloading] = useState(false);

  useEffect(() => {
    if (transferId) {
      loadTransferInfo();
    }
  }, [transferId]);

  const loadTransferInfo = async () => {
    if (!transferId) return;

    try {
      setLoading(true);
      const info = await getTransferInfo(transferId);
      setTransferInfo(info);
      setShowPasswordInput(info.hasPassword);
    } catch (error: any) {
      console.error('Error loading transfer info:', error);
      if (error.response?.status === 404) {
        setError('Transfer not found');
      } else if (error.response?.status === 410) {
        setError('Transfer has expired');
      } else {
        setError('Failed to load transfer information');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async (downloadType: 'compressed' | 'original' = 'compressed') => {
    if (!transferInfo) return;

    try {
      setDownloading(true);
      if (downloadType === 'original') {
        await downloadOriginalFile(transferInfo.transferId, transferInfo.originalName);
      } else {
        await downloadCompressedFile(transferInfo.transferId, transferInfo.originalName);
      }
    } catch (error) {
      console.error('Download error:', error);
      alert(`Failed to download ${downloadType} file`);
    } finally {
      setDownloading(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (timestamp: string | number) => {
    return new Date(timestamp).toLocaleString();
  };

  const isExpired = (expiresAt?: number) => {
    return expiresAt ? Date.now() > expiresAt : false;
  };

  const canDownload = () => {
    if (!transferInfo) return false;
    if (transferInfo.status !== 'ready') return false;
    if (isExpired(transferInfo.expiresAt)) return false;
    if (transferInfo.downloadLimit && transferInfo.downloadCount >= transferInfo.downloadLimit) return false;
    return true;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-base-200 flex items-center justify-center" data-theme="fasttransfer">
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body items-center text-center">
            <span className="loading loading-spinner loading-lg text-primary"></span>
            <h2 className="card-title">Loading transfer...</h2>
            <p className="text-base-content/70">Please wait while we fetch your file</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-base-200 flex items-center justify-center" data-theme="fasttransfer">
        <div className="card bg-base-100 shadow-xl max-w-md">
          <div className="card-body items-center text-center">
            <AlertCircle className="w-16 h-16 text-error mb-4" />
            <h2 className="card-title text-error">Transfer Not Available</h2>
            <p className="text-base-content/70">{error}</p>
            <div className="card-actions justify-end mt-4">
              <button
                onClick={() => window.location.href = '/'}
                className="btn btn-primary"
              >
                Go Home
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!transferInfo) {
    return (
      <div className="min-h-screen bg-base-200 flex items-center justify-center" data-theme="fasttransfer">
        <div className="card bg-base-100 shadow-xl max-w-md">
          <div className="card-body items-center text-center">
            <AlertCircle className="w-16 h-16 text-warning mb-4" />
            <h2 className="card-title">Transfer Not Found</h2>
            <p className="text-base-content/70">The requested transfer could not be found.</p>
            <div className="card-actions justify-end mt-4">
              <button
                onClick={() => window.location.href = '/'}
                className="btn btn-primary"
              >
                Go Home
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-base-200 py-8" data-theme="fasttransfer">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <div className="p-3 bg-primary rounded-full">
              <Zap className="w-8 h-8 text-primary-content" />
            </div>
            <h1 className="text-4xl font-bold text-base-content">FastTransfer</h1>
          </div>
          <p className="text-xl text-base-content/70">Someone shared a file with you! 📁</p>
        </div>

        {/* Main Transfer Card */}
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            {/* File Info Header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-4">
                <div className="avatar placeholder">
                  <div className="bg-primary text-primary-content rounded-full w-16">
                    <FileText className="w-8 h-8" />
                  </div>
                </div>
                <div>
                  <h2 className="card-title text-2xl">{transferInfo.originalName}</h2>
                  <div className="flex items-center space-x-2 text-base-content/70">
                    <span className="text-lg">{formatFileSize(transferInfo.size)}</span>
                    {transferInfo.compressionRatio && (
                      <>
                        <div className="divider divider-horizontal"></div>
                        <span className="text-success font-medium">
                          {Math.round((1 - transferInfo.compressionRatio) * 100)}% compressed with ZMT
                        </span>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Transfer Stats */}
            <div className="stats shadow w-full mb-6">
              <div className="stat">
                <div className="stat-figure">
                  {transferInfo.status === 'ready' ? (
                    <CheckCircle className="w-8 h-8 text-success" />
                  ) : (
                    <Clock className="w-8 h-8 text-warning" />
                  )}
                </div>
                <div className="stat-title">Status</div>
                <div className={`stat-value text-lg ${
                  transferInfo.status === 'ready' ? 'text-success' : 'text-warning'
                }`}>
                  {transferInfo.status === 'ready' ? 'Ready' : 'Processing'}
                </div>
              </div>

              <div className="stat">
                <div className="stat-figure text-primary">
                  <Clock className="w-8 h-8" />
                </div>
                <div className="stat-title">Created</div>
                <div className="stat-value text-sm">{formatDate(transferInfo.createdAt)}</div>
              </div>

              {transferInfo.expiresAt && (
                <div className="stat">
                  <div className="stat-figure">
                    <AlertCircle className={`w-8 h-8 ${
                      isExpired(transferInfo.expiresAt) ? 'text-error' : 'text-info'
                    }`} />
                  </div>
                  <div className="stat-title">Expires</div>
                  <div className={`stat-value text-sm ${
                    isExpired(transferInfo.expiresAt) ? 'text-error' : 'text-base-content'
                  }`}>
                    {formatDate(transferInfo.expiresAt)}
                  </div>
                </div>
              )}

              {transferInfo.downloadLimit && (
                <div className="stat">
                  <div className="stat-figure text-secondary">
                    <Download className="w-8 h-8" />
                  </div>
                  <div className="stat-title">Downloads</div>
                  <div className="stat-value text-lg">
                    {transferInfo.downloadCount} / {transferInfo.downloadLimit}
                  </div>
                </div>
              )}
            </div>

            {showPasswordInput && (
              <div className="alert alert-warning mb-6">
                <Lock className="w-6 h-6" />
                <div>
                  <div className="font-bold">Password Required</div>
                  <div className="text-sm">This transfer is password protected</div>
                </div>
              </div>
            )}

            {showPasswordInput && (
              <div className="form-control mb-6">
                <label className="label">
                  <span className="label-text font-medium">Enter Password</span>
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="input input-bordered w-full pr-12"
                    placeholder="Enter the password to access this file"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    {showPassword ? (
                      <EyeOff className="w-5 h-5 text-base-content/50" />
                    ) : (
                      <Eye className="w-5 h-5 text-base-content/50" />
                    )}
                  </button>
                </div>
              </div>
            )}

            <div className="card-actions justify-center">
              {canDownload() ? (
                <div className="dropdown dropdown-top">
                  <div
                    tabIndex={0}
                    role="button"
                    className={`btn btn-lg ${
                      downloading || (showPasswordInput && !password)
                        ? "btn-disabled"
                        : "btn-primary"
                    }`}
                  >
                    {downloading ? (
                      <>
                        <span className="loading loading-spinner loading-sm"></span>
                        Downloading...
                      </>
                    ) : (
                      <>
                        <Download className="w-5 h-5" />
                        Download File
                      </>
                    )}
                  </div>
                  {!downloading && !(showPasswordInput && !password) && (
                    <ul tabIndex={0} className="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                      <li><button onClick={() => handleDownload('compressed')}>
                        <Archive className="w-4 h-4" />
                        Compressed (.zmt)
                      </button></li>
                      <li><button onClick={() => handleDownload('original')}>
                        <File className="w-4 h-4" />
                        Original File
                      </button></li>
                    </ul>
                  )}
                </div>
              ) : (
                <div className="alert alert-error">
                  <AlertCircle className="w-6 h-6" />
                  <div>
                    <div className="font-bold">Download Not Available</div>
                    <div className="text-sm">
                      {isExpired(transferInfo.expiresAt)
                        ? 'This transfer has expired and is no longer available'
                        : transferInfo.downloadLimit && transferInfo.downloadCount >= transferInfo.downloadLimit
                        ? 'The download limit for this transfer has been reached'
                        : 'This transfer is not available for download'
                      }
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        <footer className="footer footer-center p-10 bg-base-200 text-base-content rounded-box mt-8">
          <div>
            <div className="flex items-center space-x-2">
              <Zap className="w-6 h-6 text-primary" />
              <span className="font-bold text-lg">FastTransfer</span>
            </div>
            <p className="text-base-content/70">
              Powered by ZMT compression technology
            </p>
            <p className="text-base-content/50 text-sm flex items-center space-x-1">
              <span>Made with</span>
              <Heart className="w-4 h-4 text-error fill-current" />
              <span>for fast, secure file sharing</span>
            </p>
          </div>
        </footer>
      </div>
    </div>
  );
}
