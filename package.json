{"name": "fast-transfer", "version": "1.0.0", "description": "WeTransfer-style file transfer platform with ZMT compression", "private": true, "workspaces": ["packages/*", "services/*"], "scripts": {"build": "npm run build --workspaces", "test": "npm run test --workspaces", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "format": "prettier --write .", "dev": "concurrently \"npm run dev --workspace=frontend\" \"npm run dev --workspace=backend\"", "deploy:infra": "cd infrastructure && npm run deploy", "deploy:backend": "cd services/backend && npm run deploy", "deploy:frontend": "cd packages/frontend && npm run build && aws s3 sync dist/ s3://fasttransfer-frontend-bucket", "test:cache": "node scripts/test-cache-behaviors.js", "test:cache:prod": "TEST_URL=https://your-domain.com node scripts/test-cache-behaviors.js", "performance": "node scripts/performance-test.js"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "concurrently": "^8.2.0", "eslint": "^8.45.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.0.0", "typescript": "^5.1.6"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}